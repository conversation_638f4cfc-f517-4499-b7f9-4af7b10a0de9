<!DOCTYPE html>
<html lang="ar">
<head>
  <meta charset="UTF-8">
  <title>حديقة الألوان السعيدة</title>
  <style>
    body {
      text-align: center;
      background-color: #fff7e6;
      font-family: "Cairo", sans-serif;
    }
    h1 {
      color: #ff6f61;
    }
    .color-button {
      width: 100px;
      height: 100px;
      margin: 10px;
      border: none;
      border-radius: 50%;
      cursor: pointer;
    }
    #red { background-color: red; }
    #blue { background-color: blue; }
    #yellow { background-color: yellow; }
  </style>
</head>
<body>
  <h1>🎨 اختر اللون الأحمر!</h1>
  <button class="color-button" id="red" onclick="checkColor('red')"></button>
  <button class="color-button" id="blue" onclick="checkColor('blue')"></button>
  <button class="color-button" id="yellow" onclick="checkColor('yellow')"></button>

  <audio id="correct" src="https://www.soundjay.com/buttons/sounds/button-3.mp3"></audio>
  <audio id="wrong" src="https://www.soundjay.com/buttons/sounds/button-10.mp3"></audio>

  <script>
    function checkColor(color) {
      if(color === "red") {
        document.getElementById("correct").play();
        alert("أحسنت! هذا هو اللون الأحمر!");
      } else {
        document.getElementById("wrong").play();
        alert("حاول مرة أخرى!");
      }
    }
  </script>
</body>
</html>
